from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from students.models import Student
from subjects.models import SubjectAssignment
from teachers.models import Teacher


class AssessmentType(models.Model):
    """أنواع التقييم"""
    
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('اسم نوع التقييم')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف نوع التقييم')
    )
    
    weight_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_('نسبة الوزن %')
    )
    
    max_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=100.00,
        verbose_name=_('الدرجة القصوى')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    class Meta:
        verbose_name = _('نوع تقييم')
        verbose_name_plural = _('أنواع التقييم')
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.weight_percentage}%)"


class Grade(models.Model):
    """الدرجات"""
    
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='grades',
        verbose_name=_('الطالب')
    )
    
    subject_assignment = models.ForeignKey(
        SubjectAssignment,
        on_delete=models.CASCADE,
        related_name='grades',
        verbose_name=_('إسناد المادة')
    )
    
    assessment_type = models.ForeignKey(
        AssessmentType,
        on_delete=models.CASCADE,
        related_name='grades',
        verbose_name=_('نوع التقييم')
    )
    
    score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('الدرجة')
    )
    
    max_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        verbose_name=_('الدرجة القصوى')
    )
    
    assessment_date = models.DateField(
        verbose_name=_('تاريخ التقييم')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    
    entered_by = models.ForeignKey(
        Teacher,
        on_delete=models.SET_NULL,
        null=True,
        related_name='entered_grades',
        verbose_name=_('أدخل بواسطة')
    )
    
    is_final = models.BooleanField(
        default=False,
        verbose_name=_('درجة نهائية')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإدخال')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    class Meta:
        verbose_name = _('درجة')
        verbose_name_plural = _('الدرجات')
        unique_together = ['student', 'subject_assignment', 'assessment_type', 'assessment_date']
        ordering = ['-assessment_date', 'student']
    
    def __str__(self):
        return f"{self.student} - {self.subject_assignment.subject.name} - {self.score}/{self.max_score}"
    
    @property
    def percentage(self):
        """حساب النسبة المئوية"""
        if self.max_score > 0:
            return (self.score / self.max_score) * 100
        return 0
    
    @property
    def letter_grade(self):
        """تحويل الدرجة إلى حرف"""
        percentage = self.percentage
        if percentage >= 90:
            return 'A+'
        elif percentage >= 85:
            return 'A'
        elif percentage >= 80:
            return 'B+'
        elif percentage >= 75:
            return 'B'
        elif percentage >= 70:
            return 'C+'
        elif percentage >= 65:
            return 'C'
        elif percentage >= 60:
            return 'D+'
        elif percentage >= 50:
            return 'D'
        else:
            return 'F'


class StudentReport(models.Model):
    """تقرير الطالب"""
    
    REPORT_TYPE_CHOICES = [
        ('midterm', _('منتصف الفصل')),
        ('final', _('نهاية الفصل')),
        ('annual', _('سنوي')),
        ('progress', _('تقرير تقدم')),
    ]
    
    student = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name='reports',
        verbose_name=_('الطالب')
    )
    
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPE_CHOICES,
        verbose_name=_('نوع التقرير')
    )
    
    academic_year = models.CharField(
        max_length=20,
        verbose_name=_('السنة الدراسية')
    )
    
    semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('الفصل الأول')),
            ('second', _('الفصل الثاني')),
            ('summer', _('الفصل الصيفي')),
        ],
        verbose_name=_('الفصل الدراسي')
    )
    
    total_score = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0.00,
        verbose_name=_('المجموع الكلي')
    )
    
    total_max_score = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0.00,
        verbose_name=_('المجموع الأقصى')
    )
    
    gpa = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=0.00,
        verbose_name=_('المعدل التراكمي')
    )
    
    rank_in_class = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('الترتيب في الفصل')
    )
    
    total_students_in_class = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_('إجمالي الطلاب في الفصل')
    )
    
    teacher_comments = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('تعليقات المعلم')
    )
    
    behavioral_grade = models.CharField(
        max_length=5,
        blank=True,
        null=True,
        verbose_name=_('درجة السلوك')
    )
    
    attendance_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=100.00,
        verbose_name=_('نسبة الحضور')
    )
    
    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    is_published = models.BooleanField(
        default=False,
        verbose_name=_('منشور')
    )
    
    class Meta:
        verbose_name = _('تقرير طالب')
        verbose_name_plural = _('تقارير الطلاب')
        unique_together = ['student', 'report_type', 'academic_year', 'semester']
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.student} - {self.get_report_type_display()} - {self.academic_year}"
    
    @property
    def percentage(self):
        """حساب النسبة المئوية الإجمالية"""
        if self.total_max_score > 0:
            return (self.total_score / self.total_max_score) * 100
        return 0
    
    @property
    def overall_grade(self):
        """الدرجة الإجمالية"""
        percentage = self.percentage
        if percentage >= 90:
            return 'ممتاز'
        elif percentage >= 80:
            return 'جيد جداً'
        elif percentage >= 70:
            return 'جيد'
        elif percentage >= 60:
            return 'مقبول'
        else:
            return 'ضعيف'


class SubjectGradeSummary(models.Model):
    """ملخص درجات المادة"""
    
    student_report = models.ForeignKey(
        StudentReport,
        on_delete=models.CASCADE,
        related_name='subject_summaries',
        verbose_name=_('تقرير الطالب')
    )
    
    subject_assignment = models.ForeignKey(
        SubjectAssignment,
        on_delete=models.CASCADE,
        related_name='grade_summaries',
        verbose_name=_('إسناد المادة')
    )
    
    total_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        verbose_name=_('المجموع')
    )
    
    total_max_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        verbose_name=_('المجموع الأقصى')
    )
    
    letter_grade = models.CharField(
        max_length=5,
        verbose_name=_('الدرجة الحرفية')
    )
    
    teacher_comment = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('تعليق المعلم')
    )
    
    class Meta:
        verbose_name = _('ملخص درجات مادة')
        verbose_name_plural = _('ملخصات درجات المواد')
        unique_together = ['student_report', 'subject_assignment']
    
    def __str__(self):
        return f"{self.student_report.student} - {self.subject_assignment.subject.name}"
    
    @property
    def percentage(self):
        """حساب النسبة المئوية"""
        if self.total_max_score > 0:
            return (self.total_score / self.total_max_score) * 100
        return 0
