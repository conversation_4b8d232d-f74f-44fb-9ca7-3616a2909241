from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import AcademicLevel, Grade, Classroom, Student, StudentAttendance


@admin.register(AcademicLevel)
class AcademicLevelAdmin(admin.ModelAdmin):
    """إدارة المراحل الدراسية"""

    list_display = ('name', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    ordering = ('name',)


@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    """إدارة الصفوف الدراسية"""

    list_display = ('name', 'academic_level', 'grade_number', 'is_active')
    list_filter = ('academic_level', 'is_active')
    search_fields = ('name',)
    ordering = ('academic_level__name', 'grade_number')


@admin.register(Classroom)
class ClassroomAdmin(admin.ModelAdmin):
    """إدارة الفصول الدراسية"""

    list_display = ('name', 'grade', 'capacity', 'student_count', 'room_number', 'is_active')
    list_filter = ('grade__academic_level', 'grade', 'is_active')
    search_fields = ('name', 'room_number')
    ordering = ('grade__academic_level__name', 'grade__name', 'name')
    
    def student_count(self, obj):
        return obj.students.filter(is_active=True).count()
    student_count.short_description = _('عدد الطلاب')


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    """إدارة الطلاب"""
    
    list_display = ('student_id', 'get_full_name', 'classroom', 'guardian_name', 'guardian_phone', 'is_active')
    list_filter = ('classroom__grade__academic_level', 'classroom__grade', 'classroom', 'is_active', 'created_at')
    search_fields = ('student_id', 'user__first_name', 'user__last_name', 'user__national_id', 'guardian_name')
    ordering = ('-created_at',)
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('user', 'student_id', 'classroom', 'gender')
        }),
        (_('معلومات ولي الأمر'), {
            'fields': ('guardian_name', 'guardian_phone', 'guardian_email')
        }),
        (_('معلومات إضافية'), {
            'fields': ('enrollment_date', 'medical_conditions', 'is_active')
        }),
    )
    
    def get_full_name(self, obj):
        return obj.user.get_full_name()
    get_full_name.short_description = _('الاسم الكامل')


@admin.register(StudentAttendance)
class StudentAttendanceAdmin(admin.ModelAdmin):
    """إدارة حضور الطلاب"""

    list_display = ('student', 'date', 'status', 'recorded_by')
    list_filter = ('status', 'date', 'student__classroom__grade')
    search_fields = ('student__user__first_name', 'student__user__last_name', 'student__student_id')
    ordering = ('-date', 'student__user__first_name')
    date_hierarchy = 'date'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'student__user', 'student__classroom', 'recorded_by'
        )
