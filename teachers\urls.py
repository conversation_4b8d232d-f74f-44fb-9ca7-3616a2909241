from django.urls import path
from . import views

app_name = 'teachers'

urlpatterns = [
    # قائمة المعلمين
    path('', views.teacher_list_view, name='teacher_list'),
    path('add/', views.add_teacher_view, name='add_teacher'),
    path('<int:teacher_id>/', views.teacher_detail_view, name='teacher_detail'),
    path('<int:teacher_id>/edit/', views.edit_teacher_view, name='edit_teacher'),
    path('<int:teacher_id>/delete/', views.delete_teacher_view, name='delete_teacher'),
    
    # المؤهلات والملاحظات
    path('<int:teacher_id>/qualifications/', views.teacher_qualifications_view, name='teacher_qualifications'),
    path('<int:teacher_id>/notes/', views.teacher_notes_view, name='teacher_notes'),
    
    # تقارير المعلمين
    path('reports/', views.teacher_reports_view, name='teacher_reports'),
]
