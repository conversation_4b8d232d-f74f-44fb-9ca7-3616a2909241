{% extends 'base.html' %}

{% block title %}لوحة تحكم المدير - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
        لوحة تحكم المدير
    </h1>
    <div class="text-muted">
        <i class="fas fa-calendar me-1"></i>
        {{ "now"|date:"Y/m/d" }}
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ total_students }}</h3>
                    <p class="mb-0">إجمالي الطلاب</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ total_teachers }}</h3>
                    <p class="mb-0">إجمالي المعلمين</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chalkboard-teacher"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ total_subjects }}</h3>
                    <p class="mb-0">المواد الدراسية</p>
                </div>
                <div class="icon">
                    <i class="fas fa-book"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ new_students }}</h3>
                    <p class="mb-0">طلاب جدد (30 يوم)</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-plus"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الرسوم البيانية -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    توزيع الطلاب حسب المرحلة الدراسية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="studentsChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- المعلمون الأكثر نشاطاً -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    المعلمون الأكثر نشاطاً
                </h5>
            </div>
            <div class="card-body">
                {% for teacher in active_teachers %}
                <div class="d-flex align-items-center mb-3">
                    {% if teacher.user.profile_picture %}
                        <img src="{{ teacher.user.profile_picture.url }}" alt="صورة المعلم" class="rounded-circle me-3" width="40" height="40">
                    {% else %}
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    {% endif %}
                    <div class="flex-grow-1">
                        <h6 class="mb-0">{{ teacher.user.get_full_name }}</h6>
                        <small class="text-muted">{{ teacher.subjects_count }} مادة</small>
                    </div>
                    <span class="badge bg-primary">{{ teacher.subjects_count }}</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد بيانات</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الإعلانات الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullhorn me-2"></i>
                    الإعلانات الحديثة
                </h5>
                <a href="{% url 'dashboard:announcements' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% for announcement in recent_announcements %}
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ announcement.title }}</h6>
                            <p class="text-muted small mb-1">{{ announcement.content|truncatewords:15 }}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ announcement.publish_date|date:"Y/m/d H:i" }}
                            </small>
                        </div>
                        <span class="badge bg-{{ announcement.priority|default:'secondary' }}">
                            {{ announcement.get_priority_display }}
                        </span>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد إعلانات</p>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- إجراءات سريعة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <a href="{% url 'students:add_student' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus d-block mb-2"></i>
                            إضافة طالب
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'teachers:add_teacher' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-chalkboard-teacher d-block mb-2"></i>
                            إضافة معلم
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'subjects:add_subject' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-book d-block mb-2"></i>
                            إضافة مادة
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'schedules:create_schedule' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-calendar-plus d-block mb-2"></i>
                            إنشاء جدول
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{% url 'accounts:register' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-users d-block mb-2"></i>
                            إضافة مستخدم
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="#" class="btn btn-outline-danger w-100" data-bs-toggle="modal" data-bs-target="#reportsModal">
                            <i class="fas fa-chart-line d-block mb-2"></i>
                            التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتقارير -->
<div class="modal fade" id="reportsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">التقارير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i>تقرير الطلاب
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-chalkboard-teacher me-2"></i>تقرير المعلمين
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i>تقرير الدرجات
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-calendar-check me-2"></i>تقرير الحضور
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // رسم بياني لتوزيع الطلاب
    const ctx = document.getElementById('studentsChart').getContext('2d');
    const studentsData = {{ students_by_level|safe }};
    
    const labels = studentsData.map(item => item.classroom__grade__academic_level__name || 'غير محدد');
    const data = studentsData.map(item => item.count);
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#667eea',
                    '#764ba2',
                    '#f093fb',
                    '#f5576c',
                    '#4facfe',
                    '#00f2fe'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
</script>
{% endblock %}
