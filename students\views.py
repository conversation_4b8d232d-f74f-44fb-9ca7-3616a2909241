from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Student, AcademicLevel, Grade, Classroom, StudentAttendance
from .forms import StudentForm, ClassroomForm, GradeForm, AttendanceForm


@login_required
def student_list_view(request):
    """قائمة الطلاب"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    students = Student.objects.filter(is_active=True).select_related(
        'user', 'classroom', 'classroom__grade'
    ).order_by('-created_at')
    
    # فلترة حسب الصف
    grade_id = request.GET.get('grade')
    if grade_id:
        students = students.filter(classroom__grade_id=grade_id)
    
    # فلترة حسب الفصل
    classroom_id = request.GET.get('classroom')
    if classroom_id:
        students = students.filter(classroom_id=classroom_id)
    
    # البحث
    search = request.GET.get('search')
    if search:
        students = students.filter(
            Q(user__first_name__icontains=search) |
            Q(user__last_name__icontains=search) |
            Q(student_id__icontains=search) |
            Q(user__national_id__icontains=search)
        )
    
    # التصفح
    paginator = Paginator(students, 20)
    page_number = request.GET.get('page')
    students = paginator.get_page(page_number)
    
    # البيانات للفلاتر
    grades = Grade.objects.filter(is_active=True)
    classrooms = Classroom.objects.filter(is_active=True).select_related('grade')
    
    context = {
        'students': students,
        'grades': grades,
        'classrooms': classrooms,
        'search': search,
        'selected_grade': grade_id,
        'selected_classroom': classroom_id,
    }
    
    return render(request, 'students/student_list.html', context)


@login_required
def add_student_view(request):
    """إضافة طالب جديد"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    if request.method == 'POST':
        form = StudentForm(request.POST, request.FILES)
        if form.is_valid():
            student = form.save()
            messages.success(request, _('تم إضافة الطالب بنجاح'))
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentForm()
    
    return render(request, 'students/add_student.html', {'form': form})


@login_required
def student_detail_view(request, student_id):
    """تفاصيل الطالب"""
    student = get_object_or_404(Student, id=student_id, is_active=True)
    
    # التحقق من الصلاحيات
    if not (request.user.is_admin or request.user.is_staff_member or 
            (request.user.is_student and request.user.student_profile == student)):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # إحصائيات الحضور
    total_attendance = StudentAttendance.objects.filter(student=student).count()
    present_count = StudentAttendance.objects.filter(student=student, status='present').count()
    attendance_percentage = (present_count / total_attendance * 100) if total_attendance > 0 else 0
    
    context = {
        'student': student,
        'attendance_percentage': attendance_percentage,
        'total_attendance': total_attendance,
        'present_count': present_count,
    }
    
    return render(request, 'students/student_detail.html', context)


@login_required
def edit_student_view(request, student_id):
    """تعديل بيانات الطالب"""
    student = get_object_or_404(Student, id=student_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    if request.method == 'POST':
        form = StudentForm(request.POST, request.FILES, instance=student)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث بيانات الطالب بنجاح'))
            return redirect('students:student_detail', student_id=student.id)
    else:
        form = StudentForm(instance=student)
    
    return render(request, 'students/edit_student.html', {'form': form, 'student': student})


@login_required
def delete_student_view(request, student_id):
    """حذف الطالب"""
    if not request.user.is_admin:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    try:
        student = Student.objects.get(id=student_id)
        student.is_active = False
        student.save()
        return JsonResponse({'success': True, 'message': 'تم حذف الطالب بنجاح'})
    except Student.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الطالب غير موجود'})


@login_required
def attendance_list_view(request):
    """قائمة الحضور والغياب"""
    if not (request.user.is_admin or request.user.is_staff_member or request.user.is_teacher):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # فلترة حسب التاريخ والفصل
    date = request.GET.get('date')
    classroom_id = request.GET.get('classroom')
    
    attendance_records = StudentAttendance.objects.select_related(
        'student__user', 'student__classroom'
    ).order_by('-date', 'student__user__first_name')
    
    if date:
        attendance_records = attendance_records.filter(date=date)
    
    if classroom_id:
        attendance_records = attendance_records.filter(student__classroom_id=classroom_id)
    
    # التصفح
    paginator = Paginator(attendance_records, 30)
    page_number = request.GET.get('page')
    attendance_records = paginator.get_page(page_number)
    
    classrooms = Classroom.objects.filter(is_active=True).select_related('grade')
    
    context = {
        'attendance_records': attendance_records,
        'classrooms': classrooms,
        'selected_date': date,
        'selected_classroom': classroom_id,
    }
    
    return render(request, 'students/attendance_list.html', context)


@login_required
def mark_attendance_view(request):
    """تسجيل الحضور والغياب"""
    if not (request.user.is_admin or request.user.is_staff_member or request.user.is_teacher):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    if request.method == 'POST':
        form = AttendanceForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تسجيل الحضور بنجاح'))
            return redirect('students:attendance_list')
    else:
        form = AttendanceForm()
    
    return render(request, 'students/mark_attendance.html', {'form': form})


@login_required
def student_attendance_view(request, student_id):
    """سجل حضور طالب معين"""
    student = get_object_or_404(Student, id=student_id, is_active=True)
    
    attendance_records = StudentAttendance.objects.filter(
        student=student
    ).order_by('-date')
    
    # التصفح
    paginator = Paginator(attendance_records, 30)
    page_number = request.GET.get('page')
    attendance_records = paginator.get_page(page_number)
    
    context = {
        'student': student,
        'attendance_records': attendance_records,
    }
    
    return render(request, 'students/student_attendance.html', context)


@login_required
def grade_list_view(request):
    """قائمة الصفوف الدراسية"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    grades = Grade.objects.filter(is_active=True).select_related('academic_level')
    
    context = {
        'grades': grades,
    }
    
    return render(request, 'students/grade_list.html', context)


@login_required
def add_grade_view(request):
    """إضافة صف دراسي جديد"""
    if not request.user.is_admin:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    if request.method == 'POST':
        form = GradeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم إضافة الصف بنجاح'))
            return redirect('students:grade_list')
    else:
        form = GradeForm()
    
    return render(request, 'students/add_grade.html', {'form': form})


@login_required
def classroom_list_view(request):
    """قائمة الفصول الدراسية"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    classrooms = Classroom.objects.filter(is_active=True).select_related('grade').annotate(
        student_count=Count('students', filter=Q(students__is_active=True))
    )
    
    context = {
        'classrooms': classrooms,
    }
    
    return render(request, 'students/classroom_list.html', context)


@login_required
def add_classroom_view(request):
    """إضافة فصل دراسي جديد"""
    if not request.user.is_admin:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    if request.method == 'POST':
        form = ClassroomForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم إضافة الفصل بنجاح'))
            return redirect('students:classroom_list')
    else:
        form = ClassroomForm()
    
    return render(request, 'students/add_classroom.html', {'form': form})
