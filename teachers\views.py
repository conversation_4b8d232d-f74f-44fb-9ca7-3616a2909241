from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Teacher, TeacherQualification, TeacherNote
from subjects.models import SubjectAssignment


@login_required
def teacher_list_view(request):
    """قائمة المعلمين"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    teachers = Teacher.objects.filter(is_active=True).select_related('user').annotate(
        subjects_count=Count('subject_assignments', filter=Q(subject_assignments__is_active=True))
    ).order_by('-created_at')
    
    # البحث
    search = request.GET.get('search')
    if search:
        teachers = teachers.filter(
            Q(user__first_name__icontains=search) |
            Q(user__last_name__icontains=search) |
            Q(employee_id__icontains=search) |
            Q(user__national_id__icontains=search)
        )
    
    # التصفح
    paginator = Paginator(teachers, 20)
    page_number = request.GET.get('page')
    teachers = paginator.get_page(page_number)
    
    context = {
        'teachers': teachers,
        'search': search,
    }
    
    return render(request, 'teachers/teacher_list.html', context)


@login_required
def add_teacher_view(request):
    """إضافة معلم جديد"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # سيتم إنشاء النموذج لاحقاً
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('teachers:teacher_list')


@login_required
def teacher_detail_view(request, teacher_id):
    """تفاصيل المعلم"""
    teacher = get_object_or_404(Teacher, id=teacher_id, is_active=True)
    
    # التحقق من الصلاحيات
    if not (request.user.is_admin or request.user.is_staff_member or 
            (request.user.is_teacher and request.user.teacher_profile == teacher)):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # المواد المُدرَّسة
    subject_assignments = SubjectAssignment.objects.filter(
        teacher=teacher, is_active=True
    ).select_related('subject', 'classroom')
    
    # المؤهلات
    qualifications = TeacherQualification.objects.filter(teacher=teacher)
    
    # الملاحظات الحديثة
    recent_notes = TeacherNote.objects.filter(teacher=teacher).order_by('-created_at')[:5]
    
    context = {
        'teacher': teacher,
        'subject_assignments': subject_assignments,
        'qualifications': qualifications,
        'recent_notes': recent_notes,
    }
    
    return render(request, 'teachers/teacher_detail.html', context)


@login_required
def edit_teacher_view(request, teacher_id):
    """تعديل بيانات المعلم"""
    teacher = get_object_or_404(Teacher, id=teacher_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # سيتم إنشاء النموذج لاحقاً
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('teachers:teacher_detail', teacher_id=teacher.id)


@login_required
def delete_teacher_view(request, teacher_id):
    """حذف المعلم"""
    if not request.user.is_admin:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    try:
        teacher = Teacher.objects.get(id=teacher_id)
        teacher.is_active = False
        teacher.save()
        return JsonResponse({'success': True, 'message': 'تم حذف المعلم بنجاح'})
    except Teacher.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'المعلم غير موجود'})


@login_required
def teacher_qualifications_view(request, teacher_id):
    """مؤهلات المعلم"""
    teacher = get_object_or_404(Teacher, id=teacher_id, is_active=True)
    
    qualifications = TeacherQualification.objects.filter(teacher=teacher).order_by('-date_obtained')
    
    context = {
        'teacher': teacher,
        'qualifications': qualifications,
    }
    
    return render(request, 'teachers/teacher_qualifications.html', context)


@login_required
def teacher_notes_view(request, teacher_id):
    """ملاحظات المعلم"""
    teacher = get_object_or_404(Teacher, id=teacher_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    notes = TeacherNote.objects.filter(teacher=teacher).order_by('-created_at')
    
    # التصفح
    paginator = Paginator(notes, 10)
    page_number = request.GET.get('page')
    notes = paginator.get_page(page_number)
    
    context = {
        'teacher': teacher,
        'notes': notes,
    }
    
    return render(request, 'teachers/teacher_notes.html', context)


@login_required
def teacher_reports_view(request):
    """تقارير المعلمين"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # سيتم تطوير التقارير لاحقاً
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('teachers:teacher_list')
