from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from students.models import Classroom
from subjects.models import SubjectAssignment
from teachers.models import Teacher


class TimeSlot(models.Model):
    """الفترات الزمنية"""
    
    name = models.CharField(
        max_length=50,
        verbose_name=_('اسم الفترة')
    )
    
    start_time = models.TimeField(
        verbose_name=_('وقت البداية')
    )
    
    end_time = models.TimeField(
        verbose_name=_('وقت النهاية')
    )
    
    period_number = models.PositiveIntegerField(
        unique=True,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        verbose_name=_('رقم الحصة')
    )
    
    is_break = models.BooleanField(
        default=False,
        verbose_name=_('فترة استراحة')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    class Meta:
        verbose_name = _('فترة زمنية')
        verbose_name_plural = _('الفترات الزمنية')
        ordering = ['period_number']
    
    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"
    
    @property
    def duration_minutes(self):
        """مدة الفترة بالدقائق"""
        from datetime import datetime, timedelta
        start = datetime.combine(datetime.today(), self.start_time)
        end = datetime.combine(datetime.today(), self.end_time)
        duration = end - start
        return int(duration.total_seconds() / 60)


class Schedule(models.Model):
    """الجدول الدراسي"""
    
    DAYS_OF_WEEK = [
        ('sunday', _('الأحد')),
        ('monday', _('الاثنين')),
        ('tuesday', _('الثلاثاء')),
        ('wednesday', _('الأربعاء')),
        ('thursday', _('الخميس')),
        ('friday', _('الجمعة')),
        ('saturday', _('السبت')),
    ]
    
    classroom = models.ForeignKey(
        Classroom,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('الفصل')
    )
    
    subject_assignment = models.ForeignKey(
        SubjectAssignment,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('إسناد المادة')
    )
    
    time_slot = models.ForeignKey(
        TimeSlot,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('الفترة الزمنية')
    )
    
    day_of_week = models.CharField(
        max_length=10,
        choices=DAYS_OF_WEEK,
        verbose_name=_('يوم الأسبوع')
    )
    
    room_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('رقم الغرفة')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    class Meta:
        verbose_name = _('جدول دراسي')
        verbose_name_plural = _('الجداول الدراسية')
        unique_together = [
            ['classroom', 'time_slot', 'day_of_week'],
            ['subject_assignment', 'time_slot', 'day_of_week']
        ]
        ordering = ['day_of_week', 'time_slot__period_number']
    
    def __str__(self):
        return f"{self.classroom} - {self.get_day_of_week_display()} - {self.time_slot}"
    
    @property
    def teacher(self):
        return self.subject_assignment.teacher
    
    @property
    def subject(self):
        return self.subject_assignment.subject


class ScheduleTemplate(models.Model):
    """قالب الجدول الدراسي"""
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم القالب')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف القالب')
    )
    
    academic_year = models.CharField(
        max_length=20,
        verbose_name=_('السنة الدراسية')
    )
    
    semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('الفصل الأول')),
            ('second', _('الفصل الثاني')),
            ('summer', _('الفصل الصيفي')),
        ],
        verbose_name=_('الفصل الدراسي')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_schedule_templates',
        verbose_name=_('أنشئ بواسطة')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    class Meta:
        verbose_name = _('قالب جدول')
        verbose_name_plural = _('قوالب الجداول')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.academic_year} - {self.get_semester_display()}"


class TeacherSchedule(models.Model):
    """جدول المعلم"""
    
    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='teacher_schedules',
        verbose_name=_('المعلم')
    )
    
    schedule = models.ForeignKey(
        Schedule,
        on_delete=models.CASCADE,
        related_name='teacher_schedules',
        verbose_name=_('الجدول')
    )
    
    is_substitute = models.BooleanField(
        default=False,
        verbose_name=_('معلم بديل')
    )
    
    substitute_for = models.ForeignKey(
        Teacher,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='substitute_schedules',
        verbose_name=_('بديل عن')
    )
    
    date_from = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('من تاريخ')
    )
    
    date_to = models.DateField(
        blank=True,
        null=True,
        verbose_name=_('إلى تاريخ')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    
    class Meta:
        verbose_name = _('جدول معلم')
        verbose_name_plural = _('جداول المعلمين')
        ordering = ['teacher', 'schedule__day_of_week', 'schedule__time_slot__period_number']
    
    def __str__(self):
        return f"{self.teacher.user.get_full_name()} - {self.schedule}"


class ScheduleConflict(models.Model):
    """تعارضات الجدول"""
    
    CONFLICT_TYPE_CHOICES = [
        ('teacher_double_booking', _('حجز مزدوج للمعلم')),
        ('classroom_double_booking', _('حجز مزدوج للفصل')),
        ('teacher_overload', _('تحميل زائد للمعلم')),
        ('time_constraint', _('قيد زمني')),
    ]
    
    conflict_type = models.CharField(
        max_length=30,
        choices=CONFLICT_TYPE_CHOICES,
        verbose_name=_('نوع التعارض')
    )
    
    schedule1 = models.ForeignKey(
        Schedule,
        on_delete=models.CASCADE,
        related_name='conflicts_as_first',
        verbose_name=_('الجدول الأول')
    )
    
    schedule2 = models.ForeignKey(
        Schedule,
        on_delete=models.CASCADE,
        related_name='conflicts_as_second',
        blank=True,
        null=True,
        verbose_name=_('الجدول الثاني')
    )
    
    description = models.TextField(
        verbose_name=_('وصف التعارض')
    )
    
    is_resolved = models.BooleanField(
        default=False,
        verbose_name=_('تم الحل')
    )
    
    resolution_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات الحل')
    )
    
    detected_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الاكتشاف')
    )
    
    resolved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الحل')
    )
    
    class Meta:
        verbose_name = _('تعارض جدول')
        verbose_name_plural = _('تعارضات الجداول')
        ordering = ['-detected_at']
    
    def __str__(self):
        return f"{self.get_conflict_type_display()} - {self.schedule1}"
