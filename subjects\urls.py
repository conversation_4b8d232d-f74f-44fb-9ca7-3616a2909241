from django.urls import path
from . import views

app_name = 'subjects'

urlpatterns = [
    # المواد الدراسية
    path('', views.subject_list_view, name='subject_list'),
    path('add/', views.add_subject_view, name='add_subject'),
    path('<int:subject_id>/', views.subject_detail_view, name='subject_detail'),
    path('<int:subject_id>/edit/', views.edit_subject_view, name='edit_subject'),
    path('<int:subject_id>/delete/', views.delete_subject_view, name='delete_subject'),
    
    # تعيين المواد للمعلمين
    path('assignments/', views.subject_assignment_list_view, name='subject_assignment_list'),
    path('assignments/add/', views.add_subject_assignment_view, name='add_subject_assignment'),
    path('assignments/<int:assignment_id>/edit/', views.edit_subject_assignment_view, name='edit_subject_assignment'),
    
    # المناهج
    path('<int:subject_id>/curriculum/', views.curriculum_view, name='curriculum'),
    path('<int:subject_id>/curriculum/add/', views.add_curriculum_view, name='add_curriculum'),
]
