{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}قائمة الطلاب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-user-graduate me-2 text-primary"></i>
        قائمة الطلاب
    </h1>
    <a href="{% url 'students:add_student' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة طالب جديد
    </a>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="اسم الطالب أو رقم الهوية" value="{{ search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">الصف الدراسي</label>
                <select name="grade" class="form-control">
                    <option value="">جميع الصفوف</option>
                    {% for grade in grades %}
                        <option value="{{ grade.id }}" {% if grade.id|stringformat:"s" == selected_grade %}selected{% endif %}>
                            {{ grade.name }} - {{ grade.academic_level.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الفصل الدراسي</label>
                <select name="classroom" class="form-control">
                    <option value="">جميع الفصول</option>
                    {% for classroom in classrooms %}
                        <option value="{{ classroom.id }}" {% if classroom.id|stringformat:"s" == selected_classroom %}selected{% endif %}>
                            {{ classroom.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{% url 'students:student_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الطلاب -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            الطلاب ({{ students.paginator.count }} طالب)
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-success btn-sm">
                <i class="fas fa-file-excel me-1"></i>تصدير Excel
            </button>
            <button type="button" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-file-pdf me-1"></i>تصدير PDF
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        {% if students %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>رقم الطالب</th>
                            <th>الاسم الكامل</th>
                            <th>الفصل الدراسي</th>
                            <th>ولي الأمر</th>
                            <th>هاتف ولي الأمر</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr>
                            <td>
                                {% if student.user.profile_picture %}
                                    <img src="{{ student.user.profile_picture.url }}" alt="صورة الطالب" class="rounded-circle" width="40" height="40">
                                {% else %}
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ student.student_id }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ student.user.get_full_name }}</strong>
                                    {% if student.user.national_id %}
                                        <br><small class="text-muted">{{ student.user.national_id }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if student.classroom %}
                                    <span class="badge bg-info">{{ student.classroom.name }}</span>
                                    <br><small class="text-muted">{{ student.classroom.grade.name }}</small>
                                {% else %}
                                    <span class="badge bg-warning">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ student.guardian_name|default:"-" }}</td>
                            <td>
                                {% if student.guardian_phone %}
                                    <a href="tel:{{ student.guardian_phone }}" class="text-decoration-none">
                                        <i class="fas fa-phone me-1"></i>{{ student.guardian_phone }}
                                    </a>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ student.created_at|date:"Y/m/d" }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'students:student_detail' student.id %}" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'students:edit_student' student.id %}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if request.user.is_admin %}
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-student" 
                                            data-student-id="{{ student.id }}" 
                                            data-student-name="{{ student.user.get_full_name }}" 
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- التصفح -->
            {% if students.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="تصفح الطلاب">
                    <ul class="pagination justify-content-center mb-0">
                        {% if students.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ students.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_classroom %}&classroom={{ selected_classroom }}{% endif %}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for num in students.paginator.page_range %}
                            {% if students.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > students.number|add:'-3' and num < students.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_classroom %}&classroom={{ selected_classroom }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if students.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ students.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_classroom %}&classroom={{ selected_classroom }}{% endif %}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلاب</h5>
                <p class="text-muted">لم يتم العثور على أي طلاب مطابقين لمعايير البحث</p>
                <a href="{% url 'students:add_student' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة أول طالب
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الطالب <strong id="studentName"></strong>؟</p>
                <p class="text-danger small">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // حذف الطالب
    let studentIdToDelete = null;
    
    document.querySelectorAll('.delete-student').forEach(button => {
        button.addEventListener('click', function() {
            studentIdToDelete = this.getAttribute('data-student-id');
            const studentName = this.getAttribute('data-student-name');
            
            document.getElementById('studentName').textContent = studentName;
            
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
    });
    
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (studentIdToDelete) {
            fetch(`/students/${studentIdToDelete}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
            
            const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            deleteModal.hide();
        }
    });
</script>
{% endblock %}
