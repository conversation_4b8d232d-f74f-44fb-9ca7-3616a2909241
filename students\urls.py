from django.urls import path
from . import views

app_name = 'students'

urlpatterns = [
    # قائمة الطلاب
    path('', views.student_list_view, name='student_list'),
    path('add/', views.add_student_view, name='add_student'),
    path('<int:student_id>/', views.student_detail_view, name='student_detail'),
    path('<int:student_id>/edit/', views.edit_student_view, name='edit_student'),
    path('<int:student_id>/delete/', views.delete_student_view, name='delete_student'),
    
    # الحضور والغياب
    path('attendance/', views.attendance_list_view, name='attendance_list'),
    path('attendance/mark/', views.mark_attendance_view, name='mark_attendance'),
    path('attendance/<int:student_id>/', views.student_attendance_view, name='student_attendance'),
    
    # إدارة الصفوف والمراحل
    path('grades/', views.grade_list_view, name='grade_list'),
    path('grades/add/', views.add_grade_view, name='add_grade'),
    path('classrooms/', views.classroom_list_view, name='classroom_list'),
    path('classrooms/add/', views.add_classroom_view, name='add_classroom'),
]
