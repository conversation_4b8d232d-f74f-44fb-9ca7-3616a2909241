from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Q
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from datetime import datetime, timedelta
from students.models import Student, AcademicLevel, Grade
from teachers.models import Teacher
from subjects.models import Subject, SubjectAssignment
from schedules.models import Schedule
from grades.models import Grade as StudentGrade
from .models import Announcement, SchoolSettings


@login_required
def home_view(request):
    """الصفحة الرئيسية للوحة التحكم"""
    user = request.user
    
    # توجيه المستخدم حسب نوعه
    if user.is_admin:
        return admin_dashboard_view(request)
    elif user.is_teacher:
        return teacher_dashboard_view(request)
    elif user.is_staff_member:
        return staff_dashboard_view(request)
    elif user.is_student:
        return student_dashboard_view(request)
    else:
        return redirect('accounts:login')


@login_required
def admin_dashboard_view(request):
    """لوحة تحكم المدير"""
    if not request.user.is_admin:
        return redirect('dashboard:home')
    
    # إحصائيات عامة
    total_students = Student.objects.filter(is_active=True).count()
    total_teachers = Teacher.objects.filter(is_active=True).count()
    total_subjects = Subject.objects.filter(is_active=True).count()
    total_schedules = Schedule.objects.filter(is_active=True).count()
    
    # إحصائيات الطلاب حسب المرحلة
    students_by_level = Student.objects.filter(is_active=True).values(
        'classroom__grade__academic_level__name'
    ).annotate(count=Count('id'))
    
    # المعلمون الأكثر نشاطاً
    active_teachers = Teacher.objects.filter(is_active=True).annotate(
        subjects_count=Count('subject_assignments', filter=Q(subject_assignments__is_active=True))
    ).order_by('-subjects_count')[:5]
    
    # الإعلانات الحديثة
    recent_announcements = Announcement.objects.filter(
        is_active=True,
        publish_date__lte=datetime.now()
    ).order_by('-publish_date')[:5]
    
    # الطلاب الجدد (آخر 30 يوم)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    new_students = Student.objects.filter(
        created_at__gte=thirty_days_ago,
        is_active=True
    ).count()
    
    context = {
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_subjects': total_subjects,
        'total_schedules': total_schedules,
        'students_by_level': students_by_level,
        'active_teachers': active_teachers,
        'recent_announcements': recent_announcements,
        'new_students': new_students,
    }
    
    return render(request, 'dashboard/admin_dashboard.html', context)


@login_required
def teacher_dashboard_view(request):
    """لوحة تحكم المعلم"""
    if not request.user.is_teacher:
        return redirect('dashboard:home')
    
    try:
        teacher = request.user.teacher_profile
    except:
        return redirect('accounts:profile')
    
    # المواد المسندة للمعلم
    assigned_subjects = SubjectAssignment.objects.filter(
        teacher=teacher,
        is_active=True
    ).select_related('subject', 'grade')
    
    # الجدول الأسبوعي
    weekly_schedule = Schedule.objects.filter(
        subject_assignment__teacher=teacher,
        is_active=True
    ).select_related('subject_assignment__subject', 'classroom', 'time_slot').order_by(
        'day_of_week', 'time_slot__period_number'
    )
    
    # إحصائيات الدرجات
    total_grades_entered = StudentGrade.objects.filter(
        entered_by=teacher
    ).count()
    
    # الطلاب في الفصول المسندة
    total_students = Student.objects.filter(
        classroom__schedules__subject_assignment__teacher=teacher,
        is_active=True
    ).distinct().count()
    
    context = {
        'teacher': teacher,
        'assigned_subjects': assigned_subjects,
        'weekly_schedule': weekly_schedule,
        'total_grades_entered': total_grades_entered,
        'total_students': total_students,
    }
    
    return render(request, 'dashboard/teacher_dashboard.html', context)


@login_required
def staff_dashboard_view(request):
    """لوحة تحكم الإداري"""
    if not request.user.is_staff_member:
        return redirect('dashboard:home')
    
    # إحصائيات الطلاب
    total_students = Student.objects.filter(is_active=True).count()
    students_by_grade = Student.objects.filter(is_active=True).values(
        'classroom__grade__name'
    ).annotate(count=Count('id'))
    
    # إحصائيات المعلمين
    total_teachers = Teacher.objects.filter(is_active=True).count()
    
    # الجداول الدراسية
    total_schedules = Schedule.objects.filter(is_active=True).count()
    
    # الطلاب الجدد هذا الشهر
    current_month = datetime.now().replace(day=1)
    new_students_this_month = Student.objects.filter(
        created_at__gte=current_month,
        is_active=True
    ).count()
    
    context = {
        'total_students': total_students,
        'students_by_grade': students_by_grade,
        'total_teachers': total_teachers,
        'total_schedules': total_schedules,
        'new_students_this_month': new_students_this_month,
    }
    
    return render(request, 'dashboard/staff_dashboard.html', context)


@login_required
def student_dashboard_view(request):
    """لوحة تحكم الطالب"""
    if not request.user.is_student:
        return redirect('dashboard:home')
    
    try:
        student = request.user.student_profile
    except:
        return redirect('accounts:profile')
    
    # معلومات الطالب
    classroom = student.classroom
    grade = classroom.grade if classroom else None
    
    # الجدول الأسبوعي للطالب
    if classroom:
        weekly_schedule = Schedule.objects.filter(
            classroom=classroom,
            is_active=True
        ).select_related('subject_assignment__subject', 'subject_assignment__teacher', 'time_slot').order_by(
            'day_of_week', 'time_slot__period_number'
        )
    else:
        weekly_schedule = []
    
    # الدرجات الحديثة
    recent_grades = StudentGrade.objects.filter(
        student=student
    ).select_related('subject_assignment__subject', 'assessment_type').order_by('-assessment_date')[:5]
    
    # الإعلانات للطلاب
    announcements = Announcement.objects.filter(
        is_active=True,
        target_audience__in=['all', 'students'],
        publish_date__lte=datetime.now()
    ).order_by('-publish_date')[:5]
    
    context = {
        'student': student,
        'classroom': classroom,
        'grade': grade,
        'weekly_schedule': weekly_schedule,
        'recent_grades': recent_grades,
        'announcements': announcements,
    }
    
    return render(request, 'dashboard/student_dashboard.html', context)


@login_required
def get_dashboard_stats(request):
    """API لإحصائيات لوحة التحكم"""
    if not request.user.is_admin:
        return JsonResponse({'error': 'غير مصرح'}, status=403)
    
    # إحصائيات الطلاب حسب المرحلة
    students_by_level = list(Student.objects.filter(is_active=True).values(
        'classroom__grade__academic_level__name'
    ).annotate(count=Count('id')))
    
    # إحصائيات الحضور (مثال)
    attendance_stats = {
        'present': 85,
        'absent': 10,
        'late': 5
    }
    
    # إحصائيات الدرجات
    grades_stats = {
        'excellent': 25,
        'good': 45,
        'average': 25,
        'poor': 5
    }
    
    return JsonResponse({
        'students_by_level': students_by_level,
        'attendance_stats': attendance_stats,
        'grades_stats': grades_stats,
    })


@login_required
def announcements_view(request):
    """عرض الإعلانات"""
    user = request.user
    
    # تحديد الجمهور المستهدف حسب نوع المستخدم
    if user.is_admin:
        target_audiences = ['all', 'admin']
    elif user.is_teacher:
        target_audiences = ['all', 'teachers']
    elif user.is_staff_member:
        target_audiences = ['all', 'staff']
    elif user.is_student:
        target_audiences = ['all', 'students']
    else:
        target_audiences = ['all']
    
    announcements = Announcement.objects.filter(
        is_active=True,
        target_audience__in=target_audiences,
        publish_date__lte=datetime.now()
    ).order_by('-publish_date')
    
    context = {
        'announcements': announcements,
    }
    
    return render(request, 'dashboard/announcements.html', context)
