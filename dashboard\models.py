from django.db import models
from django.utils.translation import gettext_lazy as _
from accounts.models import User


class Announcement(models.Model):
    """الإعلانات"""
    
    PRIORITY_CHOICES = [
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('urgent', _('عاجل')),
    ]
    
    TARGET_AUDIENCE_CHOICES = [
        ('all', _('الجميع')),
        ('students', _('الطلاب')),
        ('teachers', _('المعلمون')),
        ('staff', _('الإداريون')),
        ('parents', _('أولياء الأمور')),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الإعلان')
    )
    
    content = models.TextField(
        verbose_name=_('محتوى الإعلان')
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name=_('الأولوية')
    )
    
    target_audience = models.CharField(
        max_length=20,
        choices=TARGET_AUDIENCE_CHOICES,
        default='all',
        verbose_name=_('الجمهور المستهدف')
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_announcements',
        verbose_name=_('أنشئ بواسطة')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    publish_date = models.DateTimeField(
        verbose_name=_('تاريخ النشر')
    )
    
    expire_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الانتهاء')
    )
    
    attachment = models.FileField(
        upload_to='announcements/',
        blank=True,
        null=True,
        verbose_name=_('مرفق')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    class Meta:
        verbose_name = _('إعلان')
        verbose_name_plural = _('الإعلانات')
        ordering = ['-publish_date']
    
    def __str__(self):
        return self.title


class SystemLog(models.Model):
    """سجل النظام"""
    
    ACTION_CHOICES = [
        ('login', _('تسجيل دخول')),
        ('logout', _('تسجيل خروج')),
        ('create', _('إنشاء')),
        ('update', _('تحديث')),
        ('delete', _('حذف')),
        ('view', _('عرض')),
        ('export', _('تصدير')),
        ('import', _('استيراد')),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='system_logs',
        verbose_name=_('المستخدم')
    )
    
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name=_('الإجراء')
    )
    
    model_name = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('اسم النموذج')
    )
    
    object_id = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('معرف الكائن')
    )
    
    description = models.TextField(
        verbose_name=_('الوصف')
    )
    
    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name=_('عنوان IP')
    )
    
    user_agent = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وكيل المستخدم')
    )
    
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('الطابع الزمني')
    )
    
    class Meta:
        verbose_name = _('سجل نظام')
        verbose_name_plural = _('سجلات النظام')
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user} - {self.get_action_display()} - {self.timestamp}"


class SchoolSettings(models.Model):
    """إعدادات المدرسة"""
    
    school_name = models.CharField(
        max_length=200,
        verbose_name=_('اسم المدرسة')
    )
    
    school_logo = models.ImageField(
        upload_to='school/',
        blank=True,
        null=True,
        verbose_name=_('شعار المدرسة')
    )
    
    address = models.TextField(
        verbose_name=_('العنوان')
    )
    
    phone = models.CharField(
        max_length=20,
        verbose_name=_('الهاتف')
    )
    
    email = models.EmailField(
        verbose_name=_('البريد الإلكتروني')
    )
    
    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('الموقع الإلكتروني')
    )
    
    principal_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم المدير')
    )
    
    current_academic_year = models.CharField(
        max_length=20,
        verbose_name=_('السنة الدراسية الحالية')
    )
    
    current_semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('الفصل الأول')),
            ('second', _('الفصل الثاني')),
            ('summer', _('الفصل الصيفي')),
        ],
        default='first',
        verbose_name=_('الفصل الدراسي الحالي')
    )
    
    school_start_time = models.TimeField(
        default='07:00',
        verbose_name=_('وقت بداية الدوام')
    )
    
    school_end_time = models.TimeField(
        default='14:00',
        verbose_name=_('وقت نهاية الدوام')
    )
    
    max_students_per_class = models.PositiveIntegerField(
        default=30,
        verbose_name=_('الحد الأقصى للطلاب في الفصل')
    )
    
    grading_system = models.CharField(
        max_length=20,
        choices=[
            ('percentage', _('نسبة مئوية')),
            ('letter', _('درجات حرفية')),
            ('points', _('نقاط')),
        ],
        default='percentage',
        verbose_name=_('نظام التقييم')
    )
    
    language = models.CharField(
        max_length=10,
        choices=[
            ('ar', _('العربية')),
            ('en', _('الإنجليزية')),
        ],
        default='ar',
        verbose_name=_('اللغة')
    )
    
    timezone = models.CharField(
        max_length=50,
        default='Asia/Riyadh',
        verbose_name=_('المنطقة الزمنية')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    class Meta:
        verbose_name = _('إعدادات المدرسة')
        verbose_name_plural = _('إعدادات المدرسة')
    
    def __str__(self):
        return self.school_name
    
    def save(self, *args, **kwargs):
        # التأكد من وجود سجل واحد فقط
        if not self.pk and SchoolSettings.objects.exists():
            raise ValueError('يمكن وجود سجل واحد فقط من إعدادات المدرسة')
        super().save(*args, **kwargs)


class DashboardWidget(models.Model):
    """عناصر لوحة التحكم"""
    
    WIDGET_TYPE_CHOICES = [
        ('stats', _('إحصائيات')),
        ('chart', _('رسم بياني')),
        ('table', _('جدول')),
        ('calendar', _('تقويم')),
        ('announcements', _('إعلانات')),
        ('quick_actions', _('إجراءات سريعة')),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم العنصر')
    )
    
    widget_type = models.CharField(
        max_length=20,
        choices=WIDGET_TYPE_CHOICES,
        verbose_name=_('نوع العنصر')
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('العنوان')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('الوصف')
    )
    
    user_types = models.JSONField(
        default=list,
        verbose_name=_('أنواع المستخدمين')
    )
    
    position_x = models.PositiveIntegerField(
        default=0,
        verbose_name=_('الموضع الأفقي')
    )
    
    position_y = models.PositiveIntegerField(
        default=0,
        verbose_name=_('الموضع العمودي')
    )
    
    width = models.PositiveIntegerField(
        default=4,
        verbose_name=_('العرض')
    )
    
    height = models.PositiveIntegerField(
        default=3,
        verbose_name=_('الارتفاع')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_('الترتيب')
    )
    
    class Meta:
        verbose_name = _('عنصر لوحة التحكم')
        verbose_name_plural = _('عناصر لوحة التحكم')
        ordering = ['order']
    
    def __str__(self):
        return self.title
