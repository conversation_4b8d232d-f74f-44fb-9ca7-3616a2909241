from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from students.models import AcademicLevel, Grade
from teachers.models import Teacher


class Subject(models.Model):
    """المواد الدراسية"""
    
    SUBJECT_TYPE_CHOICES = [
        ('core', _('أساسي')),
        ('elective', _('اختياري')),
        ('activity', _('نشاط')),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم المادة')
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رمز المادة')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف المادة')
    )
    
    subject_type = models.CharField(
        max_length=20,
        choices=SUBJECT_TYPE_CHOICES,
        default='core',
        verbose_name=_('نوع المادة')
    )
    
    academic_levels = models.ManyToManyField(
        AcademicLevel,
        related_name='subjects',
        verbose_name=_('المراحل الدراسية')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    class Meta:
        verbose_name = _('مادة دراسية')
        verbose_name_plural = _('المواد الدراسية')
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class SubjectAssignment(models.Model):
    """إسناد المواد للمعلمين"""
    
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='assignments',
        verbose_name=_('المادة')
    )
    
    teacher = models.ForeignKey(
        Teacher,
        on_delete=models.CASCADE,
        related_name='subject_assignments',
        verbose_name=_('المعلم')
    )
    
    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='subject_assignments',
        verbose_name=_('الصف')
    )
    
    weekly_hours = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        verbose_name=_('عدد الحصص الأسبوعية')
    )
    
    academic_year = models.CharField(
        max_length=20,
        verbose_name=_('السنة الدراسية')
    )
    
    semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('الفصل الأول')),
            ('second', _('الفصل الثاني')),
            ('summer', _('الفصل الصيفي')),
        ],
        default='first',
        verbose_name=_('الفصل الدراسي')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    assigned_date = models.DateField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإسناد')
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )
    
    class Meta:
        verbose_name = _('إسناد مادة')
        verbose_name_plural = _('إسناد المواد')
        unique_together = ['subject', 'teacher', 'grade', 'academic_year', 'semester']
        ordering = ['academic_year', 'semester', 'subject']
    
    def __str__(self):
        return f"{self.subject.name} - {self.teacher.user.get_full_name()} - {self.grade}"


class Curriculum(models.Model):
    """المنهج الدراسي"""
    
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='curricula',
        verbose_name=_('المادة')
    )
    
    grade = models.ForeignKey(
        Grade,
        on_delete=models.CASCADE,
        related_name='curricula',
        verbose_name=_('الصف')
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان المنهج')
    )
    
    description = models.TextField(
        verbose_name=_('وصف المنهج')
    )
    
    learning_objectives = models.TextField(
        verbose_name=_('أهداف التعلم')
    )
    
    academic_year = models.CharField(
        max_length=20,
        verbose_name=_('السنة الدراسية')
    )
    
    semester = models.CharField(
        max_length=20,
        choices=[
            ('first', _('الفصل الأول')),
            ('second', _('الفصل الثاني')),
            ('summer', _('الفصل الصيفي')),
        ],
        verbose_name=_('الفصل الدراسي')
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )
    
    class Meta:
        verbose_name = _('منهج دراسي')
        verbose_name_plural = _('المناهج الدراسية')
        ordering = ['academic_year', 'semester', 'subject']
    
    def __str__(self):
        return f"{self.subject.name} - {self.grade} - {self.title}"


class CurriculumUnit(models.Model):
    """وحدات المنهج"""
    
    curriculum = models.ForeignKey(
        Curriculum,
        on_delete=models.CASCADE,
        related_name='units',
        verbose_name=_('المنهج')
    )
    
    unit_number = models.PositiveIntegerField(
        verbose_name=_('رقم الوحدة')
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الوحدة')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الوحدة')
    )
    
    estimated_hours = models.PositiveIntegerField(
        default=1,
        verbose_name=_('الساعات المقدرة')
    )
    
    learning_outcomes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('مخرجات التعلم')
    )
    
    order = models.PositiveIntegerField(
        default=1,
        verbose_name=_('الترتيب')
    )
    
    class Meta:
        verbose_name = _('وحدة منهج')
        verbose_name_plural = _('وحدات المنهج')
        ordering = ['curriculum', 'order', 'unit_number']
        unique_together = ['curriculum', 'unit_number']
    
    def __str__(self):
        return f"{self.curriculum.subject.name} - الوحدة {self.unit_number}: {self.title}"
