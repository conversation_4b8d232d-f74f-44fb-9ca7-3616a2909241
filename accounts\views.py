from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from django.http import JsonResponse
from .models import User
from .forms import LoginForm, UserRegistrationForm, UserProfileForm


@csrf_protect
@never_cache
def login_view(request):
    """صفحة تسجيل الدخول"""
    if request.user.is_authenticated:
        return redirect('dashboard:home')
    
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user_type = form.cleaned_data.get('user_type')
            
            user = authenticate(request, username=username, password=password)
            
            if user is not None:
                if user.is_active:
                    # التحقق من نوع المستخدم إذا تم تحديده
                    if user_type and user.user_type != user_type:
                        messages.error(request, _('نوع المستخدم غير صحيح'))
                        return render(request, 'accounts/login.html', {'form': form})
                    
                    login(request, user)
                    
                    # توجيه المستخدم حسب نوعه
                    if user.is_admin:
                        return redirect('dashboard:admin_dashboard')
                    elif user.is_teacher:
                        return redirect('dashboard:teacher_dashboard')
                    elif user.is_staff_member:
                        return redirect('dashboard:staff_dashboard')
                    elif user.is_student:
                        return redirect('dashboard:student_dashboard')
                    else:
                        return redirect('dashboard:home')
                else:
                    messages.error(request, _('حسابك غير نشط. يرجى الاتصال بالإدارة'))
            else:
                messages.error(request, _('اسم المستخدم أو كلمة المرور غير صحيحة'))
    else:
        form = LoginForm()
    
    return render(request, 'accounts/login.html', {'form': form})


def logout_view(request):
    """تسجيل الخروج"""
    logout(request)
    messages.success(request, _('تم تسجيل الخروج بنجاح'))
    return redirect('accounts:login')


@login_required
def profile_view(request):
    """عرض الملف الشخصي"""
    try:
        profile = request.user.profile
    except:
        profile = None
    
    context = {
        'user': request.user,
        'profile': profile,
    }
    return render(request, 'accounts/profile.html', context)


@login_required
def edit_profile_view(request):
    """تعديل الملف الشخصي"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث الملف الشخصي بنجاح'))
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=request.user)
    
    return render(request, 'accounts/edit_profile.html', {'form': form})


def register_view(request):
    """تسجيل مستخدم جديد (للإداريين فقط)"""
    if not request.user.is_authenticated or not request.user.is_admin:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('accounts:login')
    
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, _('تم إنشاء المستخدم بنجاح'))
            return redirect('accounts:user_list')
    else:
        form = UserRegistrationForm()
    
    return render(request, 'accounts/register.html', {'form': form})


@login_required
def user_list_view(request):
    """قائمة المستخدمين"""
    if not request.user.is_admin and not request.user.is_staff_member:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    users = User.objects.all().order_by('-date_joined')
    
    # فلترة حسب نوع المستخدم
    user_type = request.GET.get('user_type')
    if user_type:
        users = users.filter(user_type=user_type)
    
    # البحث
    search = request.GET.get('search')
    if search:
        users = users.filter(
            models.Q(username__icontains=search) |
            models.Q(first_name__icontains=search) |
            models.Q(last_name__icontains=search) |
            models.Q(email__icontains=search)
        )
    
    context = {
        'users': users,
        'user_type': user_type,
        'search': search,
    }
    return render(request, 'accounts/user_list.html', context)


@login_required
def toggle_user_status(request, user_id):
    """تفعيل/إلغاء تفعيل المستخدم"""
    if not request.user.is_admin:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    try:
        user = User.objects.get(id=user_id)
        user.is_active = not user.is_active
        user.save()
        
        status = 'مفعل' if user.is_active else 'معطل'
        return JsonResponse({
            'success': True, 
            'message': f'تم {status} المستخدم بنجاح',
            'is_active': user.is_active
        })
    except User.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'المستخدم غير موجود'})


# دوال مساعدة للتحقق من الصلاحيات
def user_is_admin(user):
    """التحقق من أن المستخدم مدير"""
    return user.is_authenticated and user.is_admin


def user_is_teacher(user):
    """التحقق من أن المستخدم معلم"""
    return user.is_authenticated and user.is_teacher


def user_is_staff(user):
    """التحقق من أن المستخدم إداري"""
    return user.is_authenticated and user.is_staff_member


def user_is_student(user):
    """التحقق من أن المستخدم طالب"""
    return user.is_authenticated and user.is_student


def user_can_manage_users(user):
    """التحقق من صلاحية إدارة المستخدمين"""
    return user.is_authenticated and (user.is_admin or user.is_staff_member)


def user_can_manage_students(user):
    """التحقق من صلاحية إدارة الطلاب"""
    return user.is_authenticated and (user.is_admin or user.is_staff_member)


def user_can_manage_teachers(user):
    """التحقق من صلاحية إدارة المعلمين"""
    return user.is_authenticated and user.is_admin


def user_can_manage_schedules(user):
    """التحقق من صلاحية إدارة الجداول"""
    return user.is_authenticated and (user.is_admin or user.is_staff_member)


def user_can_enter_grades(user):
    """التحقق من صلاحية إدخال الدرجات"""
    return user.is_authenticated and (user.is_admin or user.is_teacher)


def user_can_view_reports(user):
    """التحقق من صلاحية عرض التقارير"""
    return user.is_authenticated and (user.is_admin or user.is_staff_member or user.is_teacher)
