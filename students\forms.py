from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from accounts.models import User
from .models import Student, AcademicLevel, Grade, Classroom, StudentAttendance


class StudentForm(forms.ModelForm):
    """نموذج إضافة/تعديل الطالب"""
    
    # حقول المستخدم
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('اسم المستخدم')
        }),
        label=_('اسم المستخدم')
    )
    
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('الاسم الأول')
        }),
        label=_('الاسم الأول')
    )
    
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('اسم العائلة')
        }),
        label=_('اسم العائلة')
    )
    
    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('البريد الإلكتروني')
        }),
        label=_('البريد الإلكتروني')
    )
    
    phone = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('رقم الهاتف')
        }),
        label=_('رقم الهاتف')
    )
    
    national_id = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('رقم الهوية')
        }),
        label=_('رقم الهوية')
    )
    
    date_of_birth = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('تاريخ الميلاد')
    )
    
    address = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('العنوان')
        }),
        label=_('العنوان')
    )
    
    profile_picture = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control'
        }),
        label=_('صورة الطالب')
    )
    
    password = forms.CharField(
        required=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('كلمة المرور (اختيارية)')
        }),
        label=_('كلمة المرور'),
        help_text=_('اتركها فارغة لإنشاء كلمة مرور تلقائية')
    )
    
    class Meta:
        model = Student
        fields = [
            'student_id', 'classroom', 'gender', 'guardian_name', 'guardian_phone',
            'guardian_email', 'enrollment_date', 'medical_conditions'
        ]
        widgets = {
            'student_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('رقم الطالب')
            }),
            'classroom': forms.Select(attrs={
                'class': 'form-control'
            }),
            'gender': forms.Select(attrs={
                'class': 'form-control'
            }),
            'guardian_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('اسم ولي الأمر')
            }),
            'guardian_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('هاتف ولي الأمر')
            }),
            'guardian_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': _('بريد ولي الأمر')
            }),
            'enrollment_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'medical_conditions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('الحالات الطبية')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد الفصول النشطة فقط
        self.fields['classroom'].queryset = Classroom.objects.filter(is_active=True).select_related('grade')
        
        # إذا كان التعديل، املأ حقول المستخدم
        if self.instance and self.instance.pk and hasattr(self.instance, 'user'):
            user = self.instance.user
            self.fields['username'].initial = user.username
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['email'].initial = user.email
            self.fields['phone'].initial = user.phone
            self.fields['national_id'].initial = user.national_id
            self.fields['date_of_birth'].initial = user.date_of_birth
            self.fields['address'].initial = user.address
            self.fields['password'].required = False
    
    def clean_username(self):
        username = self.cleaned_data.get('username')
        if self.instance and self.instance.pk:
            # في حالة التعديل، تجاهل المستخدم الحالي
            if User.objects.filter(username=username).exclude(pk=self.instance.user.pk).exists():
                raise ValidationError(_('اسم المستخدم موجود بالفعل'))
        else:
            # في حالة الإضافة
            if User.objects.filter(username=username).exists():
                raise ValidationError(_('اسم المستخدم موجود بالفعل'))
        return username
    
    def clean_student_id(self):
        student_id = self.cleaned_data.get('student_id')
        if self.instance and self.instance.pk:
            if Student.objects.filter(student_id=student_id).exclude(pk=self.instance.pk).exists():
                raise ValidationError(_('رقم الطالب موجود بالفعل'))
        else:
            if Student.objects.filter(student_id=student_id).exists():
                raise ValidationError(_('رقم الطالب موجود بالفعل'))
        return student_id
    
    def save(self, commit=True):
        # إنشاء أو تحديث المستخدم
        if self.instance and self.instance.pk:
            # تعديل مستخدم موجود
            user = self.instance.user
        else:
            # إنشاء مستخدم جديد
            user = User(user_type='student')
        
        user.username = self.cleaned_data['username']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data.get('email', '')
        user.phone = self.cleaned_data.get('phone', '')
        user.national_id = self.cleaned_data.get('national_id', '')
        user.date_of_birth = self.cleaned_data.get('date_of_birth')
        user.address = self.cleaned_data.get('address', '')
        
        if self.cleaned_data.get('profile_picture'):
            user.profile_picture = self.cleaned_data['profile_picture']
        
        # تعيين كلمة المرور
        password = self.cleaned_data.get('password')
        if password:
            user.set_password(password)
        elif not self.instance.pk:
            # كلمة مرور تلقائية للطلاب الجدد
            user.set_password('student123')
        
        if commit:
            user.save()
        
        # حفظ بيانات الطالب
        student = super().save(commit=False)
        student.user = user
        
        if commit:
            student.save()
        
        return student


class ClassroomForm(forms.ModelForm):
    """نموذج إضافة/تعديل الفصل الدراسي"""
    
    class Meta:
        model = Classroom
        fields = ['name', 'grade', 'capacity', 'room_number', 'class_teacher']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('اسم الفصل')
            }),
            'grade': forms.Select(attrs={
                'class': 'form-control'
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': _('السعة القصوى')
            }),
            'room_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('رقم الغرفة')
            }),
            'class_teacher': forms.Select(attrs={
                'class': 'form-control'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['grade'].queryset = Grade.objects.filter(is_active=True).select_related('academic_level')


class GradeForm(forms.ModelForm):
    """نموذج إضافة/تعديل الصف الدراسي"""
    
    class Meta:
        model = Grade
        fields = ['name', 'academic_level', 'grade_number', 'max_students']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('اسم الصف')
            }),
            'academic_level': forms.Select(attrs={
                'class': 'form-control'
            }),
            'grade_number': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': _('رقم الصف')
            }),
            'max_students': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': _('الحد الأقصى للطلاب')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['academic_level'].queryset = AcademicLevel.objects.filter(is_active=True)


class AttendanceForm(forms.ModelForm):
    """نموذج تسجيل الحضور"""
    
    class Meta:
        model = StudentAttendance
        fields = ['student', 'date', 'status', 'notes']
        widgets = {
            'student': forms.Select(attrs={
                'class': 'form-control'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': _('ملاحظات')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['student'].queryset = Student.objects.filter(is_active=True).select_related('user')


class BulkAttendanceForm(forms.Form):
    """نموذج تسجيل الحضور الجماعي"""
    
    classroom = forms.ModelChoiceField(
        queryset=Classroom.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label=_('الفصل الدراسي')
    )
    
    date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('التاريخ')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة حقول الحضور لكل طالب ديناميكياً
        if 'classroom' in self.data:
            try:
                classroom_id = int(self.data.get('classroom'))
                students = Student.objects.filter(
                    classroom_id=classroom_id, 
                    is_active=True
                ).select_related('user')
                
                for student in students:
                    self.fields[f'student_{student.id}'] = forms.ChoiceField(
                        choices=StudentAttendance.STATUS_CHOICES,
                        initial='present',
                        widget=forms.Select(attrs={
                            'class': 'form-control form-control-sm'
                        }),
                        label=student.user.get_full_name()
                    )
            except (ValueError, TypeError):
                pass
