from django.urls import path
from . import views

app_name = 'schedules'

urlpatterns = [
    # الجداول الدراسية
    path('', views.schedule_list_view, name='schedule_list'),
    path('create/', views.create_schedule_view, name='create_schedule'),
    path('<int:schedule_id>/', views.schedule_detail_view, name='schedule_detail'),
    path('<int:schedule_id>/edit/', views.edit_schedule_view, name='edit_schedule'),
    path('<int:schedule_id>/delete/', views.delete_schedule_view, name='delete_schedule'),
    
    # الفترات الزمنية
    path('timeslots/', views.timeslot_list_view, name='timeslot_list'),
    path('timeslots/add/', views.add_timeslot_view, name='add_timeslot'),
    
    # قوالب الجداول
    path('templates/', views.schedule_template_list_view, name='schedule_template_list'),
    path('templates/add/', views.add_schedule_template_view, name='add_schedule_template'),
    
    # تعارضات الجداول
    path('conflicts/', views.schedule_conflicts_view, name='schedule_conflicts'),
]
