from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Avg
from .models import Grade, AssessmentType


@login_required
def grade_list_view(request):
    """قائمة الدرجات"""
    if not (request.user.is_admin or request.user.is_staff_member or request.user.is_teacher):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    grades = Grade.objects.select_related(
        'student__user', 'subject', 'assessment_type', 'teacher__user'
    ).order_by('-created_at')
    
    # فلترة للمعلمين - عرض درجات موادهم فقط
    if request.user.is_teacher:
        grades = grades.filter(teacher__user=request.user)
    
    # فلترة حسب المادة
    subject_id = request.GET.get('subject')
    if subject_id:
        grades = grades.filter(subject_id=subject_id)
    
    # فلترة حسب الطالب
    student_id = request.GET.get('student')
    if student_id:
        grades = grades.filter(student_id=student_id)
    
    # فلترة حسب نوع التقييم
    assessment_type_id = request.GET.get('assessment_type')
    if assessment_type_id:
        grades = grades.filter(assessment_type_id=assessment_type_id)
    
    # التصفح
    paginator = Paginator(grades, 30)
    page_number = request.GET.get('page')
    grades = paginator.get_page(page_number)
    
    context = {
        'grades': grades,
        'selected_subject': subject_id,
        'selected_student': student_id,
        'selected_assessment_type': assessment_type_id,
    }
    
    return render(request, 'grades/grade_list.html', context)


@login_required
def enter_grades_view(request):
    """إدخال الدرجات"""
    if not (request.user.is_admin or request.user.is_staff_member or request.user.is_teacher):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('grades:grade_list')


@login_required
def student_grades_view(request, student_id):
    """درجات طالب معين"""
    from students.models import Student
    student = get_object_or_404(Student, id=student_id, is_active=True)
    
    # التحقق من الصلاحيات
    if not (request.user.is_admin or request.user.is_staff_member or 
            (request.user.is_student and request.user.student_profile == student) or
            request.user.is_teacher):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    grades = Grade.objects.filter(student=student).select_related(
        'subject', 'assessment_type', 'teacher__user'
    ).order_by('-created_at')
    
    # حساب المتوسطات
    subject_averages = grades.values('subject__name').annotate(
        average=Avg('score')
    ).order_by('subject__name')
    
    context = {
        'student': student,
        'grades': grades,
        'subject_averages': subject_averages,
    }
    
    return render(request, 'grades/student_grades.html', context)


@login_required
def subject_grades_view(request, subject_id):
    """درجات مادة معينة"""
    from subjects.models import Subject
    subject = get_object_or_404(Subject, id=subject_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member or request.user.is_teacher):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    grades = Grade.objects.filter(subject=subject).select_related(
        'student__user', 'assessment_type', 'teacher__user'
    ).order_by('student__user__first_name')
    
    # فلترة للمعلمين
    if request.user.is_teacher:
        grades = grades.filter(teacher__user=request.user)
    
    context = {
        'subject': subject,
        'grades': grades,
    }
    
    return render(request, 'grades/subject_grades.html', context)


@login_required
def assessment_type_list_view(request):
    """قائمة أنواع التقييم"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    assessment_types = AssessmentType.objects.filter(is_active=True).order_by('name')
    
    context = {
        'assessment_types': assessment_types,
    }
    
    return render(request, 'grades/assessment_type_list.html', context)


@login_required
def add_assessment_type_view(request):
    """إضافة نوع تقييم"""
    if not request.user.is_admin:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('grades:assessment_type_list')


@login_required
def grade_reports_view(request):
    """تقارير الدرجات"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('grades:grade_list')


@login_required
def student_report_view(request, student_id):
    """تقرير طالب"""
    from students.models import Student
    student = get_object_or_404(Student, id=student_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member or 
            (request.user.is_student and request.user.student_profile == student)):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('grades:student_grades', student_id=student.id)


@login_required
def class_report_view(request, classroom_id):
    """تقرير فصل"""
    from students.models import Classroom
    classroom = get_object_or_404(Classroom, id=classroom_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('grades:grade_list')


@login_required
def export_grades_excel(request):
    """تصدير الدرجات إلى Excel"""
    if not (request.user.is_admin or request.user.is_staff_member):
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    # سيتم تطوير التصدير لاحقاً
    messages.info(request, _('هذه الميزة قيد التطوير'))
    return redirect('grades:grade_list')


@login_required
def export_grades_pdf(request):
    """تصدير الدرجات إلى PDF"""
    if not (request.user.is_admin or request.user.is_staff_member):
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    # سيتم تطوير التصدير لاحقاً
    messages.info(request, _('هذه الميزة قيد التطوير'))
    return redirect('grades:grade_list')
