from django.urls import path
from . import views

app_name = 'grades'

urlpatterns = [
    # الدرجات
    path('', views.grade_list_view, name='grade_list'),
    path('enter/', views.enter_grades_view, name='enter_grades'),
    path('student/<int:student_id>/', views.student_grades_view, name='student_grades'),
    path('subject/<int:subject_id>/', views.subject_grades_view, name='subject_grades'),
    
    # أنواع التقييم
    path('assessment-types/', views.assessment_type_list_view, name='assessment_type_list'),
    path('assessment-types/add/', views.add_assessment_type_view, name='add_assessment_type'),
    
    # التقارير
    path('reports/', views.grade_reports_view, name='grade_reports'),
    path('reports/student/<int:student_id>/', views.student_report_view, name='student_report'),
    path('reports/class/<int:classroom_id>/', views.class_report_view, name='class_report'),
    
    # تصدير البيانات
    path('export/excel/', views.export_grades_excel, name='export_grades_excel'),
    path('export/pdf/', views.export_grades_pdf, name='export_grades_pdf'),
]
