from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Subject, SubjectAssignment, Curriculum


@login_required
def subject_list_view(request):
    """قائمة المواد الدراسية"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    subjects = Subject.objects.filter(is_active=True).annotate(
        assignments_count=Count('subject_assignments', filter=Q(subject_assignments__is_active=True))
    ).order_by('name')
    
    # البحث
    search = request.GET.get('search')
    if search:
        subjects = subjects.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search) |
            Q(description__icontains=search)
        )
    
    # التصفح
    paginator = Paginator(subjects, 20)
    page_number = request.GET.get('page')
    subjects = paginator.get_page(page_number)
    
    context = {
        'subjects': subjects,
        'search': search,
    }
    
    return render(request, 'subjects/subject_list.html', context)


@login_required
def add_subject_view(request):
    """إضافة مادة دراسية جديدة"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('subjects:subject_list')


@login_required
def subject_detail_view(request, subject_id):
    """تفاصيل المادة الدراسية"""
    subject = get_object_or_404(Subject, id=subject_id, is_active=True)
    
    # التعيينات
    assignments = SubjectAssignment.objects.filter(
        subject=subject, is_active=True
    ).select_related('teacher__user', 'classroom')
    
    # المنهج
    curriculum_items = Curriculum.objects.filter(subject=subject).order_by('order')
    
    context = {
        'subject': subject,
        'assignments': assignments,
        'curriculum_items': curriculum_items,
    }
    
    return render(request, 'subjects/subject_detail.html', context)


@login_required
def edit_subject_view(request, subject_id):
    """تعديل المادة الدراسية"""
    subject = get_object_or_404(Subject, id=subject_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('subjects:subject_detail', subject_id=subject.id)


@login_required
def delete_subject_view(request, subject_id):
    """حذف المادة الدراسية"""
    if not request.user.is_admin:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    try:
        subject = Subject.objects.get(id=subject_id)
        subject.is_active = False
        subject.save()
        return JsonResponse({'success': True, 'message': 'تم حذف المادة بنجاح'})
    except Subject.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'المادة غير موجودة'})


@login_required
def subject_assignment_list_view(request):
    """قائمة تعيينات المواد"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    assignments = SubjectAssignment.objects.filter(is_active=True).select_related(
        'subject', 'teacher__user', 'classroom'
    ).order_by('subject__name')
    
    context = {
        'assignments': assignments,
    }
    
    return render(request, 'subjects/assignment_list.html', context)


@login_required
def add_subject_assignment_view(request):
    """إضافة تعيين مادة"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('subjects:subject_assignment_list')


@login_required
def edit_subject_assignment_view(request, assignment_id):
    """تعديل تعيين المادة"""
    assignment = get_object_or_404(SubjectAssignment, id=assignment_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('subjects:subject_assignment_list')


@login_required
def curriculum_view(request, subject_id):
    """منهج المادة"""
    subject = get_object_or_404(Subject, id=subject_id, is_active=True)
    
    curriculum_items = Curriculum.objects.filter(subject=subject).order_by('order')
    
    context = {
        'subject': subject,
        'curriculum_items': curriculum_items,
    }
    
    return render(request, 'subjects/curriculum.html', context)


@login_required
def add_curriculum_view(request, subject_id):
    """إضافة عنصر منهج"""
    subject = get_object_or_404(Subject, id=subject_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member or 
            (request.user.is_teacher and 
             SubjectAssignment.objects.filter(subject=subject, teacher__user=request.user, is_active=True).exists())):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('subjects:curriculum', subject_id=subject.id)
