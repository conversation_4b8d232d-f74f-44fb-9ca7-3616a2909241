<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة المدرسة{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 25px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
            border-radius: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .stats-card .icon {
            font-size: 3rem;
            opacity: 0.8;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user.is_authenticated %}
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-white shadow-sm">
            <div class="container-fluid">
                <button class="btn btn-outline-primary d-lg-none" type="button" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <a class="navbar-brand text-primary" href="{% url 'dashboard:home' %}">
                    <i class="fas fa-graduation-cap me-2"></i>
                    نظام إدارة المدرسة
                </a>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-dark d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            {% if user.profile_picture %}
                                <img src="{{ user.profile_picture.url }}" alt="صورة المستخدم" class="user-avatar me-2">
                            {% else %}
                                <i class="fas fa-user-circle fa-2x me-2 text-primary"></i>
                            {% endif %}
                            <span>{{ user.get_full_name|default:user.username }}</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-lg-2 sidebar" id="sidebar">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            {% block sidebar %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'dashboard:home' %}">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        لوحة التحكم
                                    </a>
                                </li>
                                
                                {% if user.is_admin or user.is_staff_member %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'students:student_list' %}">
                                        <i class="fas fa-user-graduate me-2"></i>
                                        إدارة الطلاب
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% if user.is_admin %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'teachers:teacher_list' %}">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>
                                        إدارة المعلمين
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% if user.is_admin or user.is_staff_member %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'subjects:subject_list' %}">
                                        <i class="fas fa-book me-2"></i>
                                        المواد الدراسية
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% if user.is_admin or user.is_staff_member %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'schedules:schedule_list' %}">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        الجداول الدراسية
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% if user.is_teacher or user.is_admin %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'grades:grade_list' %}">
                                        <i class="fas fa-chart-line me-2"></i>
                                        الدرجات
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% if user.is_admin %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{% url 'accounts:user_list' %}">
                                        <i class="fas fa-users me-2"></i>
                                        إدارة المستخدمين
                                    </a>
                                </li>
                                {% endif %}
                            {% endblock %}
                        </ul>
                    </div>
                </nav>
                
                <!-- Main content -->
                <main class="col-lg-10 ms-sm-auto main-content">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    {% else %}
        {% block content %}{% endblock %}
    {% endif %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for dashboard charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
