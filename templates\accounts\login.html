{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}تسجيل الدخول - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="login-container d-flex align-items-center justify-content-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="login-card">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <i class="fas fa-graduation-cap fa-4x text-primary mb-3"></i>
                            <h2 class="fw-bold text-primary">نظام إدارة المدرسة</h2>
                            <p class="text-muted">مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
                        </div>
                        
                        <!-- Login Form -->
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            
                            <!-- User Type Selection -->
                            <div class="mb-4">
                                <label for="{{ form.user_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tag me-2"></i>نوع المستخدم
                                </label>
                                {{ form.user_type|add_class:"form-select" }}
                            </div>
                            
                            <!-- Username -->
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-2"></i>اسم المستخدم
                                </label>
                                {{ form.username|add_class:"form-control" }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Password -->
                            <div class="mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <div class="input-group">
                                    {{ form.password|add_class:"form-control" }}
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Remember Me -->
                            <div class="mb-4">
                                <div class="form-check">
                                    {{ form.remember_me|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                                        تذكرني
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>
                        
                        <!-- Quick Login Options -->
                        <div class="mt-4">
                            <hr>
                            <p class="text-center text-muted small mb-3">تسجيل دخول سريع</p>
                            <div class="row g-2">
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-primary btn-sm w-100 quick-login" data-type="admin">
                                        <i class="fas fa-user-shield me-1"></i>مدير
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-success btn-sm w-100 quick-login" data-type="teacher">
                                        <i class="fas fa-chalkboard-teacher me-1"></i>معلم
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-info btn-sm w-100 quick-login" data-type="staff">
                                        <i class="fas fa-user-tie me-1"></i>إداري
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-warning btn-sm w-100 quick-login" data-type="student">
                                        <i class="fas fa-user-graduate me-1"></i>طالب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="text-center mt-4">
                    <p class="text-white-50 small">
                        © 2024 نظام إدارة المدرسة. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('{{ form.password.id_for_label }}');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Quick login buttons
    document.querySelectorAll('.quick-login').forEach(button => {
        button.addEventListener('click', function() {
            const userType = this.getAttribute('data-type');
            document.getElementById('{{ form.user_type.id_for_label }}').value = userType;
            
            // Remove active class from all buttons
            document.querySelectorAll('.quick-login').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to clicked button
            this.classList.add('active');
        });
    });
    
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            const forms = document.getElementsByClassName('needs-validation');
            Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
    
    // Auto-focus on username field
    document.getElementById('{{ form.username.id_for_label }}').focus();
</script>
{% endblock %}
