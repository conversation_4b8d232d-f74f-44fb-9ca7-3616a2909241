from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Schedule, TimeSlot, ScheduleTemplate


@login_required
def schedule_list_view(request):
    """قائمة الجداول الدراسية"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    schedules = Schedule.objects.filter(is_active=True).select_related(
        'classroom', 'subject', 'teacher__user', 'time_slot'
    ).order_by('day_of_week', 'time_slot__start_time')
    
    # فلترة حسب الفصل
    classroom_id = request.GET.get('classroom')
    if classroom_id:
        schedules = schedules.filter(classroom_id=classroom_id)
    
    # فلترة حسب المعلم
    teacher_id = request.GET.get('teacher')
    if teacher_id:
        schedules = schedules.filter(teacher_id=teacher_id)
    
    # فلترة حسب اليوم
    day = request.GET.get('day')
    if day:
        schedules = schedules.filter(day_of_week=day)
    
    context = {
        'schedules': schedules,
        'selected_classroom': classroom_id,
        'selected_teacher': teacher_id,
        'selected_day': day,
    }
    
    return render(request, 'schedules/schedule_list.html', context)


@login_required
def create_schedule_view(request):
    """إنشاء جدول دراسي"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('schedules:schedule_list')


@login_required
def schedule_detail_view(request, schedule_id):
    """تفاصيل الجدول الدراسي"""
    schedule = get_object_or_404(Schedule, id=schedule_id, is_active=True)
    
    context = {
        'schedule': schedule,
    }
    
    return render(request, 'schedules/schedule_detail.html', context)


@login_required
def edit_schedule_view(request, schedule_id):
    """تعديل الجدول الدراسي"""
    schedule = get_object_or_404(Schedule, id=schedule_id, is_active=True)
    
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('schedules:schedule_detail', schedule_id=schedule.id)


@login_required
def delete_schedule_view(request, schedule_id):
    """حذف الجدول الدراسي"""
    if not request.user.is_admin:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})
    
    try:
        schedule = Schedule.objects.get(id=schedule_id)
        schedule.is_active = False
        schedule.save()
        return JsonResponse({'success': True, 'message': 'تم حذف الجدول بنجاح'})
    except Schedule.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الجدول غير موجود'})


@login_required
def timeslot_list_view(request):
    """قائمة الفترات الزمنية"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    timeslots = TimeSlot.objects.filter(is_active=True).order_by('start_time')
    
    context = {
        'timeslots': timeslots,
    }
    
    return render(request, 'schedules/timeslot_list.html', context)


@login_required
def add_timeslot_view(request):
    """إضافة فترة زمنية"""
    if not request.user.is_admin:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('schedules:timeslot_list')


@login_required
def schedule_template_list_view(request):
    """قائمة قوالب الجداول"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    templates = ScheduleTemplate.objects.filter(is_active=True).order_by('-created_at')
    
    context = {
        'templates': templates,
    }
    
    return render(request, 'schedules/template_list.html', context)


@login_required
def add_schedule_template_view(request):
    """إضافة قالب جدول"""
    if not request.user.is_admin:
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    messages.info(request, _('هذه الصفحة قيد التطوير'))
    return redirect('schedules:schedule_template_list')


@login_required
def schedule_conflicts_view(request):
    """تعارضات الجداول"""
    if not (request.user.is_admin or request.user.is_staff_member):
        messages.error(request, _('ليس لديك صلاحية للوصول لهذه الصفحة'))
        return redirect('dashboard:home')
    
    # البحث عن التعارضات
    conflicts = []
    
    # تعارضات المعلمين (نفس المعلم في نفس الوقت)
    teacher_conflicts = Schedule.objects.filter(is_active=True).values(
        'teacher', 'day_of_week', 'time_slot'
    ).annotate(count=Count('id')).filter(count__gt=1)
    
    # تعارضات الفصول (نفس الفصل في نفس الوقت)
    classroom_conflicts = Schedule.objects.filter(is_active=True).values(
        'classroom', 'day_of_week', 'time_slot'
    ).annotate(count=Count('id')).filter(count__gt=1)
    
    context = {
        'teacher_conflicts': teacher_conflicts,
        'classroom_conflicts': classroom_conflicts,
    }
    
    return render(request, 'schedules/conflicts.html', context)
